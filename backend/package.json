{"name": "gas-system-project", "version": "1.0.1", "description": "Backend API for Gas Delivery System - A comprehensive platform for managing gas cylinder orders, inventory, and delivery operations with role-based access control", "main": "dist/server.js", "keywords": ["gas-delivery", "inventory-management", "order-management", "express", "typescript", "mongodb", "jwt-authentication", "role-based-access", "api", "backend"], "scripts": {"format": "prettier --write .", "dev": "NODE_ENV=development nodemon", "start": "NODE_ENV=production node dist/server.js", "start:dev": "NODE_ENV=development node dist/server.js", "start:prod": "NODE_ENV=production node dist/server.js", "build": "tsc && cpy \"public/**/*\" dist/public --parents && cpy \"uploads/**/*\" dist/uploads --parents", "build:prod": "NODE_ENV=production npm run build", "postinstall": "npm run build", "test": "jest", "test:watch": "jest --watch"}, "engines": {"node": "20.x"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@types/cors": "^2.8.19", "axios": "^1.11.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.4.0", "form-data": "^4.0.4", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "multer": "^2.0.2", "querystring": "^0.2.1", "winston": "^3.17.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^2.0.0", "@types/node": "^24.0.1", "cpy-cli": "^5.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "nodemon": "^3.1.10", "prettier": "^3.5.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}