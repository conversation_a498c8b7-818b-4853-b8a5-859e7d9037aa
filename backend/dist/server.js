"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
process.on('unhandledRejection', err => {
    console.error('💥 Unhandled Promise Rejection:', err);
});
process.on('uncaughtException', err => {
    console.error('💥 Uncaught Exception:', err);
});
const app_1 = __importDefault(require("./app"));
const db_1 = require("./config/db");
const env_config_1 = require("./config/env_config");
const logger_1 = __importDefault(require("./config/logger"));
// Initialize database connection when the module is loaded
const startServer = async () => {
    console.log('🚀🚀🚀 RAILWAY DEPLOYMENT CHECK v2: LATEST CODE WITH REVENUE FIX 🚀🚀🚀');
    console.log('🔥 COMMIT: 6a02a4b - REVENUE CALCULATION FIXED');
    console.log('� SUPERVISOR DASHBOARD: totalRevenue should be 13 (NOT 39)');
    console.log('❌ FAILED/CANCELLED orders are EXCLUDED from revenue');
    console.log('📅 DEPLOYMENT TIME:', new Date().toISOString());
    console.log('🎯 IF YOU SEE THIS MESSAGE, RAILWAY IS USING LATEST CODE!');
    try {
        let port = env_config_1.config.server.port;
        let serverUrl = env_config_1.config.server.url;
        await (0, db_1.connectDb)();
        app_1.default.listen(port, '0.0.0.0', () => {
            // console.log(`Server is running on http://localhost:${port}`);
            // console.log(`Server is running on ${serverUrl}`);
            logger_1.default.info(`Server is running on ${serverUrl}`);
            //
        });
    }
    catch (error) {
        // console.error(`Failed to connect to the database: ${error.message}`);
        logger_1.default.error(`Failed to connect to the database: ${error.message}`);
        process.exit(1); // Exit the process if the database connection fails
    }
};
startServer();
/*


               ? INVENTORY DOCUMENTATION
 🧩 Example Lifecycle
| Step            | Method Used    | Inventory Impact                                    |
| --------------- | -------------- | --------------------------------------------------- |
| Order placed    | `reserve()`    | `reserved += qty`                                   |
| Order cancelled | `release()`    | `reserved -= qty`                                   |
| Order delivered | `markAsSold()` | `quantity -= qty`, `reserved -= qty`, `sold += qty` |

🚦 Summary
| Method       | Purpose                             | Affects Fields                             |
| ------------ | ----------------------------------- | ------------------------------------------ |
| `reserve`    | Temporarily hold items for an order | `reserved` (+)                             |
| `release`    | Undo a reservation                  | `reserved` (-)                             |
| `markAsSold` | Finalize sale after delivery        | `quantity` (-), `reserved` (-), `sold` (+) |


*/
//# sourceMappingURL=server.js.map