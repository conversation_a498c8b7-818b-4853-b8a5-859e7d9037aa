import { Cylinder, Package } from '../models/index';
import { BadRequestError, NotFoundError, DuplicateResourceError } from '../errors/app_errors';
import mongoose, { ClientSession, Types } from 'mongoose';
import { CylinderMaterial, CylinderType, CylinderStatus, UserRole } from '../enums/enums';
import { ICylinder } from '../models/cylinder.model';
import { getCylinderImageUrl, ImageUrlGenerator } from '../utils/image-url-generator';
import { FileCleanupService } from '../utils/file_cleanup';
import logger from '../config/logger';

class CylinderService {
  /**
   * Reserve cylinders for an order
   * @throws {BadRequestError} If quantity is invalid or insufficient stock
   * @throws {NotFoundError} If cylinder not found
   */
  async reserveCylinder(
    cylinderId: string,
    quantity: number,
    session?: ClientSession
  ): Promise<{ modifiedCount: number; newStatus?: CylinderStatus }> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const cylinder = await Cylinder.findById(cylinderId).session(session || null);
    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    if (cylinder.status !== CylinderStatus.Active) {
      throw new BadRequestError(`Cannot reserve cylinder with status ${cylinder.status}`, {
        code: 'INVALID_CYLINDER_STATUS',
      });
    }

    if (cylinder.availableQuantity < quantity) {
      throw new BadRequestError('Insufficient cylinder stock', {
        code: 'INSUFFICIENT_CYLINDER_STOCK',
        details: {
          available: cylinder.availableQuantity,
          requested: quantity,
        },
      });
    }

    const update: any = {
      $inc: { reserved: quantity },
    };

    // Update status if this reservation brings us to out of stock
    if (cylinder.quantity - (cylinder.reserved + quantity) <= 0) {
      update.$set = { status: CylinderStatus.OutOfStock };
    }

    const result = await Cylinder.updateOne(
      {
        _id: cylinderId,
        $expr: { $gte: [{ $subtract: ['$quantity', '$reserved'] }, quantity] },
      },
      update,
      { session }
    );

    if (result.modifiedCount === 0) {
      throw new DuplicateResourceError('Cylinder stock changed during reservation', {
        code: 'CONCURRENT_MODIFICATION',
      });
    }

    return {
      modifiedCount: result.modifiedCount,
      newStatus: update.$set?.status,
    };
  }

  /**
   * Release reserved cylinders (when order is cancelled)
   * @throws {BadRequestError} If quantity is invalid or exceeds reserved amount
   * @throws {NotFoundError} If cylinder not found
   */
  async releaseReservation(
    cylinderId: string,
    quantity: number,
    session?: ClientSession
  ): Promise<{ modifiedCount: number; newStatus?: CylinderStatus }> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const cylinder = await Cylinder.findById(cylinderId).session(session || null);
    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    if (cylinder.reserved < quantity) {
      throw new BadRequestError('Cannot release more than reserved quantity', {
        code: 'INVALID_RESERVATION_RELEASE',
        details: {
          reserved: cylinder.reserved,
          toRelease: quantity,
        },
      });
    }

    const update: any = {
      $inc: { reserved: -quantity },
    };

    // Update status if we're coming back from out of stock
    if (
      cylinder.status === CylinderStatus.OutOfStock &&
      cylinder.quantity - (cylinder.reserved - quantity) > 0
    ) {
      update.$set = { status: CylinderStatus.Active };
    }

    const result = await Cylinder.updateOne(
      { _id: cylinderId, reserved: { $gte: quantity } },
      update,
      { session }
    );

    if (result.modifiedCount === 0) {
      throw new DuplicateResourceError('Cylinder stock changed during release', {
        code: 'CONCURRENT_MODIFICATION',
      });
    }

    return {
      modifiedCount: result.modifiedCount,
      newStatus: update.$set?.status,
    };
  }

  /**
   * Mark reserved cylinders as sold (when order is completed)
   * @throws {BadRequestError} If quantity is invalid
   * @throws {NotFoundError} If cylinder not found
   */
  async markAsSold(
    cylinderId: string,
    quantity: number,
    session?: ClientSession
  ): Promise<ICylinder> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const cylinder = await Cylinder.findByIdAndUpdate(
      cylinderId,
      {
        $inc: {
          reserved: -quantity,
          sold: quantity,
          quantity: -quantity, // Physical count decreases when sold
        },
      },
      { new: true, session }
    );

    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    return cylinder;
  }

  /**
   * Restock cylinders and update status if needed
   * @throws {BadRequestError} If quantity is invalid
   * @throws {NotFoundError} If cylinder not found
   */
  async restock(cylinderId: string, quantity: number, session?: ClientSession): Promise<ICylinder> {
    if (quantity <= 0) {
      throw new BadRequestError('Quantity must be positive', {
        code: 'INVALID_QUANTITY',
      });
    }

    const update: any = {
      $inc: { quantity },
      lastRestockedAt: new Date(),
    };

    // Update status if coming from out of stock
    update.$set = {
      status: CylinderStatus.Active,
    };

    const cylinder = await Cylinder.findByIdAndUpdate(cylinderId, update, { new: true, session });

    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    return cylinder;
  }

  /**
   * Create a new cylinder type
   * @throws {DuplicateResourceError} If cylinder type already exists
   */
  async createCylinder(cylinderData: {
    type: CylinderType;
    material: CylinderMaterial;
    price: number;
    cost: number;
    imageUrl?: string;
    imagePath?: string; // New field for uploaded images
    description?: string;
    quantity?: number;
    minimumStockLevel?: number;
    status?: CylinderStatus;
  }): Promise<ICylinder> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      // Check if cylinder type already exists
      const existing = await Cylinder.findOne({
        type: cylinderData.type,
        material: cylinderData.material,
      }).session(session);

      if (existing) {
        throw new DuplicateResourceError('Cylinder type already exists', {
          code: 'CYLINDER_EXISTS',
        });
      }

      // Handle image URL logic - prioritize uploaded images over generated ones
      let finalImageUrl = cylinderData.imageUrl;
      if (!cylinderData.imagePath && !cylinderData.imageUrl) {
        // Generate dynamic image URL only if no uploaded image or custom URL provided
        finalImageUrl = getCylinderImageUrl(cylinderData.type, cylinderData.material);
      }

      const cylinder = new Cylinder({
        quantity: 0,
        reserved: 0,
        sold: 0,
        minimumStockLevel: 10,
        status: CylinderStatus.Active,
        ...cylinderData,
        imageUrl: finalImageUrl,
        // imagePath is already included in cylinderData if provided
      });

      await cylinder.save({ session });

      await session.commitTransaction();
      return cylinder;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Update cylinder details (excluding inventory-related fields)
   * @throws {NotFoundError} If cylinder not found
   */
  async updateCylinder(cylinderId: string, updateData: Partial<ICylinder>): Promise<ICylinder> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      // Prevent updating inventory-related fields through this method
      const { quantity, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;

      const cylinder = await Cylinder.findByIdAndUpdate(cylinderId, safeUpdateData, {
        new: true,
        runValidators: true,
        session,
      });

      if (!cylinder) {
        throw new NotFoundError('Cylinder not found', {
          code: 'CYLINDER_NOT_FOUND',
        });
      }

      await session.commitTransaction();
      return cylinder;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Soft delete a cylinder type (only if no active reservations)
   * @throws {NotFoundError} If cylinder not found
   * @throws {BadRequestError} If cylinder has active reservations or is linked to packages
   */
  async deleteCylinder(cylinderId: string, deletedBy?: string): Promise<ICylinder> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      // Check if cylinder exists and has no reservations
      const existing = await Cylinder.findOne({
        _id: cylinderId,
        isDeleted: false,
      }).session(session);

      if (!existing) {
        throw new NotFoundError('Cylinder not found', {
          code: 'CYLINDER_NOT_FOUND',
        });
      }

      if (existing.reserved > 0) {
        throw new BadRequestError('Cannot delete cylinder with active reservations', {
          code: 'ACTIVE_RESERVATIONS',
          details: {
            reserved: existing.reserved,
          },
        });
      }

      // Check if cylinder is linked to any packages
      const linkedPackages = await Package.find({
        cylinder: cylinderId,
        isDeleted: false,
      })
        .select('name')
        .session(session);

      if (linkedPackages.length > 0) {
        const packageNames = linkedPackages.map(pkg => pkg.name).join(', ');
        throw new BadRequestError(
          `Cannot delete cylinder. It is linked to the following package${linkedPackages.length > 1 ? 's' : ''}: ${packageNames}`,
          {
            code: 'LINKED_TO_PACKAGES',
            details: {
              linkedPackages: linkedPackages.map(pkg => ({
                id: pkg._id,
                name: pkg.name,
              })),
            },
          }
        );
      }

      // Perform soft delete
      const cylinder = await Cylinder.findByIdAndUpdate(
        cylinderId,
        {
          isDeleted: true,
          deletedAt: new Date(),
          deletedBy: deletedBy,
          status: CylinderStatus.Discontinued, // Mark as discontinued when deleted
        },
        { new: true, session }
      );

      await session.commitTransaction();
      return cylinder!;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Restore a soft-deleted cylinder
   * @throws {NotFoundError} If cylinder not found or not deleted
   */
  async restoreCylinder(cylinderId: string): Promise<ICylinder> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      const cylinder = await Cylinder.findOneAndUpdate(
        {
          _id: cylinderId,
          isDeleted: true,
        },
        {
          isDeleted: false,
          deletedAt: undefined,
          deletedBy: undefined,
          status: CylinderStatus.Active, // Restore to active status
        },
        { new: true, session }
      );

      if (!cylinder) {
        throw new NotFoundError('Deleted cylinder not found', {
          code: 'DELETED_CYLINDER_NOT_FOUND',
        });
      }

      await session.commitTransaction();
      return cylinder;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Permanently delete a cylinder (hard delete)
   * Also deletes associated image file from uploads directory
   * @throws {NotFoundError} If cylinder not found
   * @throws {BadRequestError} If cylinder is linked to packages
   */
  async permanentlyDeleteCylinder(cylinderId: string): Promise<ICylinder> {
    const session = await mongoose.startSession();
    try {
      session.startTransaction();

      // Check if cylinder is linked to any packages (even if soft-deleted)
      const linkedPackages = await Package.find({
        cylinder: cylinderId,
      })
        .select('name isDeleted')
        .session(session);

      if (linkedPackages.length > 0) {
        const activePackages = linkedPackages.filter(pkg => !pkg.isDeleted);
        const deletedPackages = linkedPackages.filter(pkg => pkg.isDeleted);

        let errorMessage = 'Cannot permanently delete cylinder. It is linked to packages.';
        const details: any = { linkedPackages: [] };

        if (activePackages.length > 0) {
          const activeNames = activePackages.map(pkg => pkg.name).join(', ');
          errorMessage += ` Active packages: ${activeNames}.`;
          details.activePackages = activePackages.map(pkg => ({ id: pkg._id, name: pkg.name }));
        }

        if (deletedPackages.length > 0) {
          const deletedNames = deletedPackages.map(pkg => pkg.name).join(', ');
          errorMessage += ` Deleted packages: ${deletedNames}.`;
          details.deletedPackages = deletedPackages.map(pkg => ({ id: pkg._id, name: pkg.name }));
        }

        errorMessage += ' Remove cylinder from packages first.';

        throw new BadRequestError(errorMessage, {
          code: 'LINKED_TO_PACKAGES',
          details,
        });
      }

      const cylinder = await Cylinder.findOneAndDelete({
        _id: cylinderId,
        isDeleted: true, // Only allow permanent deletion of soft-deleted items
      }).session(session);

      if (!cylinder) {
        throw new NotFoundError('Deleted cylinder not found', {
          code: 'DELETED_CYLINDER_NOT_FOUND',
        });
      }

      await session.commitTransaction();

      // After successful database deletion, clean up the image file
      if (cylinder.imagePath) {
        logger.info('Cleaning up cylinder image file', {
          cylinderId: cylinder._id,
          imagePath: cylinder.imagePath,
        });

        const deleted = await FileCleanupService.deleteImageFile(cylinder.imagePath);
        if (deleted) {
          // Optionally clean up empty directories
          await FileCleanupService.cleanupEmptyDirectories(cylinder.imagePath);
        }
      }

      return cylinder;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }

  /**
   * Get packages that use a specific cylinder
   * @param cylinderId - The cylinder ID to check
   * @param includeDeleted - Whether to include deleted packages
   * @returns Array of packages that use this cylinder
   */
  async getPackagesUsingCylinder(
    cylinderId: string | Types.ObjectId,
    includeDeleted = false
  ): Promise<Array<{ id: string; name: string; isDeleted: boolean }>> {
    const query: any = { cylinder: cylinderId };
    if (!includeDeleted) {
      query.isDeleted = false;
    }

    const packages = await Package.find(query).select('name isDeleted');

    return packages.map(pkg => ({
      id: pkg._id.toString(),
      name: pkg.name,
      isDeleted: pkg.isDeleted || false,
    }));
  }

  /**
   * Get cylinder by ID (excludes soft-deleted items)
   * @throws {NotFoundError} If cylinder not found
   */
  async getCylinderById(cylinderId: string, includeDeleted: boolean = false): Promise<ICylinder> {
    const query: any = { _id: cylinderId };
    if (!includeDeleted) {
      query.isDeleted = false;
    }

    const cylinder = await Cylinder.findOne(query);
    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }
    return cylinder;
  }

  /**
   * List all cylinders with optional filtering and pagination (excludes soft-deleted items)
   */
  async listCylinders(
    filter: {
      type?: CylinderType;
      material?: CylinderMaterial;
      status?: CylinderStatus;
      lowStockOnly?: boolean;
      includeDeleted?: boolean;
    } = {},
    pagination: { page: number; limit: number } = { page: 1, limit: 10 },
    requestedUser: { userId: string | Types.ObjectId; role: UserRole }
  ): Promise<{ data: ICylinder[]; total: number }> {
    const query: any = {};

    // Exclude soft-deleted items by default
    if (!filter.includeDeleted) {
      query.isDeleted = false;
    }

    if (filter.type) query.type = filter.type;
    if (filter.material) query.material = filter.material;
    // if (filter.status) query.status = filter.status;
    if (filter.status) {
      if (requestedUser.role === UserRole.CUSTOMER) {
        query.status = CylinderStatus.Active;
      } else {
        query.status = filter.status;
      }
    }
    if (filter.lowStockOnly) {
      query.$expr = { $lte: ['$quantity', '$minimumStockLevel'] };
    }

    const [data, total] = await Promise.all([
      Cylinder.find(query)
        .sort({ type: 1, material: 1 })
        .skip((pagination.page - 1) * pagination.limit)
        .limit(pagination.limit),
      Cylinder.countDocuments(query),
    ]);

    return { data, total };
  }

  /**
   * List soft-deleted cylinders with pagination
   */
  async listDeletedCylinders(
    pagination: { page: number; limit: number } = { page: 1, limit: 10 }
  ): Promise<{ data: ICylinder[]; total: number }> {
    const query = { isDeleted: true };

    const [data, total] = await Promise.all([
      Cylinder.find(query)
        .sort({ deletedAt: -1 }) // Most recently deleted first
        .skip((pagination.page - 1) * pagination.limit)
        .limit(pagination.limit),
      Cylinder.countDocuments(query),
    ]);

    return { data, total };
  }

  /**
   * Check cylinder availability
   * @throws {NotFoundError} If cylinder not found
   */
  async checkAvailability(
    cylinderId: string,
    quantity: number
  ): Promise<{ available: boolean; availableQuantity: number; status: CylinderStatus }> {
    const cylinder = await Cylinder.findById(cylinderId);
    if (!cylinder) {
      throw new NotFoundError('Cylinder not found', {
        code: 'CYLINDER_NOT_FOUND',
      });
    }

    return {
      available:
        cylinder.status === CylinderStatus.Active && cylinder.availableQuantity >= quantity,
      availableQuantity: cylinder.availableQuantity,
      status: cylinder.status,
    };
  }

  /**
   * Get low stock alerts (quantity <= minimumStockLevel)
   */
  async getLowStockAlerts(): Promise<ICylinder[]> {
    return Cylinder.find({
      $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
      status: CylinderStatus.Active, // Only active cylinders
    }).sort({ quantity: 1 }); // Sort by most critical first
  }

  /**
   * Get sales statistics aggregated by type and material
   */
  async getSalesStatistics(): Promise<{
    totalSold: number;
    byType: Record<CylinderType, number>;
    byMaterial: Record<CylinderMaterial, number>;
    byStatus: Record<CylinderStatus, number>;
  }> {
    const result = {
      totalSold: 0,
      byType: {} as Record<CylinderType, number>,
      byMaterial: {} as Record<CylinderMaterial, number>,
      byStatus: {} as Record<CylinderStatus, number>,
    };

    // Initialize all possible values with 0
    Object.values(CylinderType).forEach(type => (result.byType[type] = 0));
    Object.values(CylinderMaterial).forEach(material => (result.byMaterial[material] = 0));
    Object.values(CylinderStatus).forEach(status => (result.byStatus[status] = 0));

    const cylinders = await Cylinder.find();

    cylinders.forEach(cylinder => {
      result.totalSold += cylinder.sold;
      result.byType[cylinder.type] += cylinder.sold;
      result.byMaterial[cylinder.material] += cylinder.sold;
      result.byStatus[cylinder.status]++;
    });

    return result;
  }

  /**
   * Bulk update cylinder statuses
   * @throws {BadRequestError} If invalid status transition
   */
  async bulkUpdateStatus(
    cylinderIds: string[],
    newStatus: CylinderStatus,
    session?: ClientSession
  ): Promise<number> {
    // Validate status transitions
    if (newStatus === CylinderStatus.Active) {
      // Can only activate cylinders that are out of stock
      const result = await Cylinder.updateMany(
        {
          _id: { $in: cylinderIds },
          status: { $in: [CylinderStatus.OutOfStock] },
        },
        { $set: { status: newStatus } },
        { session }
      );
      return result.modifiedCount;
    } else {
      // For other statuses (Discontinued)
      const result = await Cylinder.updateMany(
        { _id: { $in: cylinderIds } },
        { $set: { status: newStatus } },
        { session }
      );
      return result.modifiedCount;
    }
  }

  /**
   * Get cylinders with enhanced image URLs (prioritizes uploaded images)
   * This method adds the best available image URL to each cylinder
   */
  async getCylindersWithImages(
    filter: any = {},
    options: { page?: number; limit?: number; sort?: any } = {}
  ): Promise<{ cylinders: any[]; total: number; page: number; totalPages: number }> {
    const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
    const skip = (page - 1) * limit;

    // Base query excludes soft-deleted items
    const query = { isDeleted: false, ...filter };

    const [cylinders, total] = await Promise.all([
      Cylinder.find(query).sort(sort).skip(skip).limit(limit).lean(),
      Cylinder.countDocuments(query),
    ]);

    // Enhance each cylinder with the best available image URL
    const enhancedCylinders = cylinders.map((cylinder: any) => ({
      ...cylinder,
      bestImageUrl: ImageUrlGenerator.getBestImageUrl(
        cylinder.imagePath,
        cylinder.imageUrl,
        'cylinder'
      ),
    }));

    return {
      cylinders: enhancedCylinders,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }
}

export const cylinderService = new CylinderService();
