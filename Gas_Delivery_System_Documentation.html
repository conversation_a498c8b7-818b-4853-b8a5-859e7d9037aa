<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gas Delivery System - Complete Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #007bff;
            font-size: 2.5em;
            margin: 0;
        }
        .header p {
            color: #666;
            font-size: 1.2em;
            margin: 10px 0 0 0;
        }
        h2 {
            color: #007bff;
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-top: 40px;
        }
        h3 {
            color: #495057;
            margin-top: 30px;
        }
        .role-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .role-card.customer { border-left-color: #007bff; }
        .role-card.agent { border-left-color: #28a745; }
        .role-card.supervisor { border-left-color: #ffc107; }
        .role-card.admin { border-left-color: #dc3545; }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-card h4 {
            color: #007bff;
            margin-top: 0;
        }
        .workflow-step {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .tech-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .permissions-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .permissions-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .permissions-list li {
            margin: 5px 0;
        }
        .allowed { color: #28a745; }
        .restricted { color: #dc3545; }
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
            padding-left: 20px;
        }
        .toc a {
            text-decoration: none;
            color: #007bff;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Gas Delivery System</h1>
            <p>Complete System Documentation & User Guide</p>
            <p><strong>Version 1.0</strong> | <em>Comprehensive Role-Based Gas Cylinder Delivery Platform</em></p>
        </div>

        <div class="toc">
            <h2>📋 Table of Contents</h2>
            <ul>
                <li><a href="#overview">1. System Overview</a></li>
                <li><a href="#architecture">2. Technical Architecture</a></li>
                <li><a href="#user-roles">3. User Roles & Permissions</a></li>
                <li><a href="#core-features">4. Core Features</a></li>
                <li><a href="#workflows">5. Business Workflows</a></li>
                <li><a href="#interfaces">6. User Interfaces</a></li>
                <li><a href="#admin-features">7. Administrative Features</a></li>
                <li><a href="#technical-specs">8. Technical Specifications</a></li>
            </ul>
        </div>

        <div class="page-break"></div>

        <section id="overview">
            <h2>🎯 1. System Overview</h2>
            <p>The Gas Delivery System is a comprehensive full-stack application designed to streamline gas cylinder delivery operations. It provides a complete ecosystem for customers to place orders, agents to manage deliveries, supervisors to oversee operations, and administrators to manage the entire system.</p>
            
            <h3>Key Capabilities</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🔐 Multi-Role Access Control</h4>
                    <p>Four distinct user roles (Customer, Agent, Supervisor, Admin) with specific permissions and tailored interfaces.</p>
                </div>
                <div class="feature-card">
                    <h4>📦 Real-time Inventory Management</h4>
                    <p>Live stock tracking with automatic reservation, release, and sales tracking across multiple product categories.</p>
                </div>
                <div class="feature-card">
                    <h4>🛒 Complete Order Lifecycle</h4>
                    <p>From order creation through payment processing to delivery confirmation with QR code verification.</p>
                </div>
                <div class="feature-card">
                    <h4>💳 Secure Payment Integration</h4>
                    <p>WaaFi payment gateway integration with preauthorization, capture, and failure handling.</p>
                </div>
                <div class="feature-card">
                    <h4>📱 Mobile-First Design</h4>
                    <p>Responsive Flutter frontend optimized for mobile devices with cross-platform compatibility.</p>
                </div>
                <div class="feature-card">
                    <h4>📊 Advanced Analytics</h4>
                    <p>Role-specific dashboards with sales metrics, inventory insights, and performance analytics.</p>
                </div>
            </div>
        </section>

        <div class="page-break"></div>

        <section id="architecture">
            <h2>🛠 2. Technical Architecture</h2>
            
            <h3>Technology Stack</h3>
            <div class="tech-stack">
                <div class="tech-item">
                    <h4>Frontend</h4>
                    <p><strong>Flutter</strong><br>Cross-platform mobile app with responsive design</p>
                </div>
                <div class="tech-item">
                    <h4>Backend</h4>
                    <p><strong>Node.js + Express</strong><br>RESTful API with TypeScript</p>
                </div>
                <div class="tech-item">
                    <h4>Database</h4>
                    <p><strong>MongoDB</strong><br>NoSQL database with transactions</p>
                </div>
                <div class="tech-item">
                    <h4>Authentication</h4>
                    <p><strong>JWT</strong><br>Secure token-based authentication</p>
                </div>
                <div class="tech-item">
                    <h4>Payments</h4>
                    <p><strong>WaaFi Gateway</strong><br>Mobile money integration</p>
                </div>
                <div class="tech-item">
                    <h4>Notifications</h4>
                    <p><strong>FCM + SMS</strong><br>Push notifications and SMS alerts</p>
                </div>
            </div>

            <h3>System Architecture</h3>
            <div class="workflow-step">
                <strong>Client Layer:</strong> Flutter mobile application with role-based UI components and state management using BLoC pattern.
            </div>
            <div class="workflow-step">
                <strong>API Layer:</strong> Express.js REST API with middleware for authentication, authorization, logging, and error handling.
            </div>
            <div class="workflow-step">
                <strong>Business Logic:</strong> Service layer handling order processing, inventory management, payment integration, and notification delivery.
            </div>
            <div class="workflow-step">
                <strong>Data Layer:</strong> MongoDB with Mongoose ODM, featuring transaction support and optimized indexing for performance.
            </div>
            <div class="workflow-step">
                <strong>External Services:</strong> WaaFi payment gateway, Hormuud SMS service, and Firebase Cloud Messaging for notifications.
            </div>
        </section>

        <div class="page-break"></div>

        <section id="user-roles">
            <h2>👥 3. User Roles & Permissions</h2>
            
            <div class="role-card customer">
                <h3>🛍️ Customer Role</h3>
                <p><strong>Primary Function:</strong> Browse products, place orders, and track deliveries</p>
                
                <div class="permissions-list">
                    <h4>✅ Allowed Actions:</h4>
                    <ul>
                        <li class="allowed">Browse gas cylinders, spare parts, and packages</li>
                        <li class="allowed">Add products to cart and place orders</li>
                        <li class="allowed">Make payments through WaaFi gateway</li>
                        <li class="allowed">Track order status in real-time</li>
                        <li class="allowed">View order history and details</li>
                        <li class="allowed">Generate and display QR codes for delivery verification</li>
                        <li class="allowed">Manage delivery addresses</li>
                        <li class="allowed">Update profile information</li>
                    </ul>
                    
                    <h4>❌ Restrictions:</h4>
                    <ul>
                        <li class="restricted">Cannot access inventory management</li>
                        <li class="restricted">Cannot view other customers' orders</li>
                        <li class="restricted">Cannot access admin or agent features</li>
                        <li class="restricted">Cannot modify product prices or inventory</li>
                    </ul>
                </div>
                
                <h4>Interface Features:</h4>
                <p>Three-tab navigation: <strong>Home</strong> (product catalog), <strong>Orders</strong> (order tracking), <strong>Profile</strong> (account management)</p>
            </div>

            <div class="role-card agent">
                <h3>🚚 Agent Role</h3>
                <p><strong>Primary Function:</strong> Manage assigned deliveries and complete order fulfillment</p>
                
                <div class="permissions-list">
                    <h4>✅ Allowed Actions:</h4>
                    <ul>
                        <li class="allowed">View assigned delivery orders</li>
                        <li class="allowed">Scan QR codes for delivery verification</li>
                        <li class="allowed">Update order status (In Transit, Delivered)</li>
                        <li class="allowed">Access delivery dashboard with performance metrics</li>
                        <li class="allowed">View today's delivery schedule</li>
                        <li class="allowed">Update location and on-duty status</li>
                        <li class="allowed">Access customer contact information for deliveries</li>
                        <li class="allowed">View delivery history and earnings</li>
                    </ul>
                    
                    <h4>❌ Restrictions:</h4>
                    <ul>
                        <li class="restricted">Cannot create or modify orders</li>
                        <li class="restricted">Cannot access inventory management</li>
                        <li class="restricted">Cannot view financial data (costs, profits)</li>
                        <li class="restricted">Cannot assign orders to other agents</li>
                    </ul>
                </div>
                
                <h4>Interface Features:</h4>
                <p>Three-tab navigation: <strong>Dashboard</strong> (metrics & today's summary), <strong>Deliveries</strong> (active orders), <strong>Profile</strong> (agent settings)</p>
            </div>
